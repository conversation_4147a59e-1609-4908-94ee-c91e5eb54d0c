from django.db import transaction
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from apps.booking.models import BookingBlock, Booking
from services.su_api import confirm_or_cancel_booking
from apps.integrations.utils import log_action
from apps.booking.tasks.inventory import block_rooms_task, unblock_rooms_task, update_inventory_for_booking
import json
import logging
from typing import Dict, Any
from uuid import UUID

logger = logging.getLogger(__name__)

class BookingError(Exception):
    """Custom exception for booking-related errors."""
    pass

class JSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle UUID serialization."""
    def default(self, obj):
        if isinstance(obj, UUID):
            return str(obj)
        return super().default(obj)

def serialize_details(details: Dict[str, Any]) -> Dict[str, Any]:
    """Serialize details dictionary to ensure JSON compatibility."""
    return json.loads(json.dumps(details, cls=JSONEncoder))

def log_booking_event(user, property_id: str, action: str, description: str, status: str, details: Dict) -> None:
    """Safely log booking events with proper serialization."""
    try:
        serialized_details = serialize_details(details)
        transaction.on_commit(lambda: log_action(
            user=user,
            property_id=property_id,
            action=action,
            description=description,
            status=status,
            details=serialized_details
        ))
    except Exception as e:
        logger.error(
            "Failed to log booking event",
            exc_info=True,
            extra={
                'property_id': str(property_id),
                'action': action,
                'error': str(e)
            }
        )

def _process_reservation_update(instance: Booking, status: str) -> bool:
    """Process reservation status update with proper error handling."""
    if not instance.reservation_data or not instance.reservation_data.id:
        logger.error("No reservation data found for booking", extra={'booking_id': str(instance.id)})
        return False

    try:
        response = confirm_or_cancel_booking(
            booking_id=instance.reservation_data.id
        )
        success = response.get("Success") == "Success"

        transaction.on_commit(lambda: log_booking_event(
            user=instance.property.staffs.first(),
            property_id=instance.property.id,
            action="update",
            description=f"Booking {instance.status} on SU API",
            status="successful" if success else "failed",
            details={
                "reservation_id": str(instance.id),
                "su_response": response
            }
        ))

        return success

    except Exception as e:
        logger.error(
            "Failed to update reservation",
            exc_info=True,
            extra={
                'booking_id': str(instance.id),
                'status': status,
                'error': str(e)
            }
        )
        return False

@receiver(post_save, sender=Booking)
def handle_requested_booking_confirmation(sender, instance: Booking, created: bool, **kwargs) -> None:
    """Signal handler to confirm bookings with 'requested' status."""
    if not created and instance.status == Booking.Status.REQUEST:
        transaction.on_commit(lambda: _process_reservation_update(instance, Booking.Status.REQUEST))

@receiver(post_delete, sender=BookingBlock)
def handle_booking_block_deletion(sender, instance: BookingBlock, **kwargs):
    """
    Handle booking block deletion by unblocking rooms and syncing inventory.
    """
    from .tasks.inventory import sync_single_property_inventory
    
    # Unblock the rooms first
    transaction.on_commit(lambda: unblock_rooms_task.delay(
        str(instance.property.id),
        instance.start_date,
        instance.end_date
    ))
    

@receiver(post_save, sender=BookingBlock)
def handle_booking_block_creation(sender, instance: BookingBlock, created: bool, **kwargs):
    """Handle booking block creation."""
    if created and instance.property.is_onboarded:
        transaction.on_commit(lambda: block_rooms_task.delay(instance.id))

@receiver(post_save, sender=Booking)
def handle_booking_inventory_update(sender, instance: Booking, created: bool, **kwargs):
    """
    Signal handler to update inventory when bookings are created, modified, or cancelled.

    """       
    if created or instance.status in [Booking.Status.NEW, Booking.Status.MODIFIED]:
        # For new or modified bookings, decrease inventory
        transaction.on_commit(lambda: update_inventory_for_booking.delay(str(instance.id), "decrease"))
    elif instance.status == Booking.Status.CANCELLED:
        # For cancelled bookings, increase inventory
        transaction.on_commit(lambda: update_inventory_for_booking.delay(str(instance.id), "increase"))